USB 2.0 的供电规格如下：
    - 标准输出电流：500mA (0.5A)
    - 工作电压：5V

USB 3.0 供电规格如下：
    - 标准输出电流：900mA (0.9A)
    - 工作电压：5V

ESP32 在运行 WiFi 功能时的电流消耗主要取决于以下几种工作模式：
    1. 正常工作模式（Active Mode）：
        - WiFi 收发数据时：150-260mA
        - WiFi 空闲连接时：约 100mA
    2. Modem-sleep 模式：
        - 保持 WiFi 连接但降低功耗：15-68mA
        - CPU 仍然运行，适合需要保持连接但不频繁传输数据的场景
    3. Light-sleep 模式：
        - CPU 暂停，WiFi 维持连接：0.8-3mA
        - 可以通过中断唤醒
    4. Deep-sleep 模式：
        - WiFi 完全关闭：约 10μA-50μA
        - 需要通过定时器或外部中断唤醒

SG90 舵机的电流消耗情况如下：
    1. 静止状态（无负载）：
        - 待机电流：约 5-10mA
    2. 正常工作时（运动状态）：
        - 空载运行：约 100-150mA
        - 带负载运行：约 200-250mA
    3. 堵转状态（卡住时）：
        - 可能达到 500-700mA

简单结论:
    Quad 四足机器人 1 个 ESP32 和 8 个舵机, 正常工作下, 每一个按 250ma 算, 需要大约 250 x 9 = 2250ma(2.25A)
