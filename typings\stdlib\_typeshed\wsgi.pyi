# Types to support PEP 3333 (WSGI)
#
# Obsolete since Python 3.11: Use wsgiref.types instead.
#
# See the README.md file in this directory for more information.

import sys
from _typeshed import OptExcInfo
from collections.abc import Callable, Iterable, Iterator
from typing import Any, Protocol
from typing_extensions import TypeAlias

class _Readable(Protocol):
    def read(self, size: int = ..., /) -> bytes: ...
    # Optional: def close(self) -> object: ...

if sys.version_info >= (3, 11):
    pass
else:
    # stable
    class StartResponse(Protocol):
        def __call__(
            self, status: str, headers: list[tuple[str, str]], exc_info: OptExcInfo | None = ..., /
        ) -> Callable[[bytes], object]: ...

    WSGIEnvironment: TypeAlias = dict[str, Any]  # stable
    WSGIApplication: TypeAlias = Callable[[WSGIEnvironment, StartResponse], Iterable[bytes]]  # stable

    # WSGI input streams per PEP 3333, stable
    class InputStream(Protocol):
        def read(self, size: int = ..., /) -> bytes: ...
        def readline(self, size: int = ..., /) -> bytes: ...
        def readlines(self, hint: int = ..., /) -> list[bytes]: ...
        def __iter__(self) -> Iterator[bytes]: ...

    # WSGI error streams per PEP 3333, stable
    class ErrorStream(Protocol):
        def flush(self) -> object: ...
        def write(self, s: str, /) -> object: ...
        def writelines(self, seq: list[str], /) -> object: ...

    # Optional file wrapper in wsgi.file_wrapper
    class FileWrapper(Protocol):
        def __call__(self, file: _Readable, block_size: int = ..., /) -> Iterable[bytes]: ...
