from _typeshed import Incomplete

class Response:
    raw: Incomplete
    encoding: str
    _cached: Incomplete
    def __init__(self, f) -> None: ...
    def close(self) -> None: ...
    @property
    def content(self): ...
    @property
    def text(self): ...
    def json(self): ...

def request(
    method,
    url,
    data: Incomplete | None = None,
    json: Incomplete | None = None,
    headers: Incomplete | None = None,
    stream: Incomplete | None = None,
    auth: Incomplete | None = None,
    timeout: Incomplete | None = None,
    parse_headers: bool = True,
): ...
def head(url, **kw): ...
def get(url, **kw): ...
def post(url, **kw): ...
def put(url, **kw): ...
def patch(url, **kw): ...
def delete(url, **kw): ...
