import espnow
from _typeshed import Incomplete
from collections.abc import Generator

class AIOESPNow(espnow.ESPNow):
    async def arecv(self) -> Generator[Incomplete, None, Incomplete]: ...
    async def airecv(self) -> Generator[Incomplete, None, Incomplete]: ...
    async def asend(
        self, mac, msg: Incomplete | None = None, sync: Incomplete | None = None
    ) -> Generator[Incomplete, None, Incomplete]: ...
    def __aiter__(self): ...
    async def __anext__(self): ...
