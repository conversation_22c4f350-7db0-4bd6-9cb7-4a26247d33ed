"""
Module: 'logging' on micropython-v1.25.0-esp32-ESP32_GENERIC-SPIRAM
"""

# MCU: {'variant': 'SPIRAM', 'build': '', 'arch': 'xtensawin', 'port': 'esp32', 'board': 'ESP32_GENERIC', 'board_id': 'ESP32_GENERIC-SPIRAM', 'mpy': 'v6.3', 'ver': '1.25.0', 'family': 'micropython', 'cpu': 'ESP32', 'version': '1.25.0'}
# Stubber: v1.25.0
from __future__ import annotations
from typing import Final
from _typeshed import Incomplete

CRITICAL: Final[int] = 50
ERROR: Final[int] = 40
WARNING: Final[int] = 30
INFO: Final[int] = 20
_default_datefmt: str = "%Y-%m-%d %H:%M:%S"
NOTSET: Final[int] = 0
_default_fmt: str = "%(levelname)s:%(name)s:%(message)s"
_loggers: dict = {}
_level_dict: dict = {}
DEBUG: Final[int] = 10

def critical(*args, **kwargs) -> Incomplete: ...
def getLogger(*args, **kwargs) -> Incomplete: ...
def log(*args, **kwargs) -> Incomplete: ...
def shutdown(*args, **kwargs) -> Incomplete: ...
def const(*args, **kwargs) -> Incomplete: ...
def info(*args, **kwargs) -> Incomplete: ...
def addLevelName(*args, **kwargs) -> Incomplete: ...
def debug(*args, **kwargs) -> Incomplete: ...
def warning(*args, **kwargs) -> Incomplete: ...
def error(*args, **kwargs) -> Incomplete: ...
def exception(*args, **kwargs) -> Incomplete: ...
def basicConfig(*args, **kwargs) -> Incomplete: ...

_stream: Incomplete  ## <class 'TextIOWrapper'> = <io.TextIOWrapper 2>

class LogRecord:
    def set(self, *args, **kwargs) -> Incomplete: ...
    def __init__(self, *argv, **kwargs) -> None: ...

class Handler:
    def setFormatter(self, *args, **kwargs) -> Incomplete: ...
    def setLevel(self, *args, **kwargs) -> Incomplete: ...
    def format(self, *args, **kwargs) -> Incomplete: ...
    def close(self, *args, **kwargs) -> Incomplete: ...
    def __init__(self, *argv, **kwargs) -> None: ...

class Logger:
    def setLevel(self, *args, **kwargs) -> Incomplete: ...
    def hasHandlers(self, *args, **kwargs) -> Incomplete: ...
    def critical(self, *args, **kwargs) -> Incomplete: ...
    def getEffectiveLevel(self, *args, **kwargs) -> Incomplete: ...
    def addHandler(self, *args, **kwargs) -> Incomplete: ...
    def isEnabledFor(self, *args, **kwargs) -> Incomplete: ...
    def log(self, *args, **kwargs) -> Incomplete: ...
    def warning(self, *args, **kwargs) -> Incomplete: ...
    def info(self, *args, **kwargs) -> Incomplete: ...
    def error(self, *args, **kwargs) -> Incomplete: ...
    def exception(self, *args, **kwargs) -> Incomplete: ...
    def debug(self, *args, **kwargs) -> Incomplete: ...
    def __init__(self, *argv, **kwargs) -> None: ...

class Formatter:
    def usesTime(self, *args, **kwargs) -> Incomplete: ...
    def formatTime(self, *args, **kwargs) -> Incomplete: ...
    def format(self, *args, **kwargs) -> Incomplete: ...
    def __init__(self, *argv, **kwargs) -> None: ...

class StreamHandler:
    def setLevel(self, *args, **kwargs) -> Incomplete: ...
    def setFormatter(self, *args, **kwargs) -> Incomplete: ...
    def emit(self, *args, **kwargs) -> Incomplete: ...
    def format(self, *args, **kwargs) -> Incomplete: ...
    def close(self, *args, **kwargs) -> Incomplete: ...
    def __init__(self, *argv, **kwargs) -> None: ...

class FileHandler:
    def setLevel(self, *args, **kwargs) -> Incomplete: ...
    def setFormatter(self, *args, **kwargs) -> Incomplete: ...
    def emit(self, *args, **kwargs) -> Incomplete: ...
    def format(self, *args, **kwargs) -> Incomplete: ...
    def close(self, *args, **kwargs) -> Incomplete: ...
    def __init__(self, *argv, **kwargs) -> None: ...
