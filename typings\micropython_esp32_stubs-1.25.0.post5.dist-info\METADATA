Metadata-Version: 2.3
Name: micropython-esp32-stubs
Version: 1.25.0.post5
Summary: MicroPython stubs
License: MIT
Author: <PERSON><PERSON>-email: josver<PERSON>@users.noreply.github.com
Classifier: Typing :: Stubs Only
Classifier: Development Status :: 5 - Production/Stable
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: Implementation :: MicroPython
Classifier: Operating System :: OS Independent
Classifier: Topic :: Text Editors :: Integrated Development Environments (IDE)
Classifier: Topic :: Software Development :: Documentation
Classifier: Topic :: Software Development :: Embedded Systems
Classifier: Topic :: Software Development :: Testing
Classifier: Natural Language :: English
Requires-Dist: micropython-stdlib-stubs (>=1.24.0)
Project-URL: Documentation, https://micropython-stubs.readthedocs.io/
Project-URL: Homepage, https://github.com/josverl/micropython-stubs#micropython-stubs
Project-URL: Repository, https://github.com/josverl/micropython-stubs
Description-Content-Type: text/markdown

# micropython-esp32-stubs


This is a stub-only package for MicroPython.
It is intended to be installed in a projects virtual environment to allow static type checkers and intellisense features to be used while writing Micropython code.

The version of this package is alligned the the version of the MicroPython firmware.
 - Major, Minor and Patch levels are alligned to the same version as the firmware.  
 - The post release level is used to publish new releases of the stubs.

For `Micropython 1.17` the stubs are published as `1.17.post1` ... `1.17.post2`  
for `Micropython 1.18` the stubs are published as `1.18.post1` ... `1.18.post2`  

To install the latest stubs:  
`pip install -I  micropython-<port>-stubs` where port is the port of the MicroPython firmware.

To install the stubs for an older version, such as MicroPython 1.17:  
`pip install micropython-stm32-stubs==1.17.*` which will install the last post release of the stubs for MicroPython 1.17.


As the creation of the stubs, and merging of the different types is still going though improvements, the stub packages are marked as Beta.
To upgrade stubs to the latest stubs for a specific version use `pip install micropython-stm32-stubs==1.17.* --upgrade`

If you have suggestions or find any issues with the stubs, please report them in the [MicroPython-stubs Discussions](https://github.com/Josverl/micropython-stubs/discussions)

For an overview of  Micropython Stubs please see: https://micropython-stubs.readthedocs.io/en/main/ 
 * List of all stubs : https://micropython-stubs.readthedocs.io/en/main/firmware_grp.html



Included stubs:
* Merged stubs from `stubs/micropython-v1_25_0-esp32-ESP32_GENERIC-merged`
* Frozen stubs from `stubs/micropython-v1_25_0-frozen/esp32/GENERIC`
* Core stubs from `stubs/micropython-core`


origin | Family | Port | Board | Version
-------|--------|------|-------|--------

